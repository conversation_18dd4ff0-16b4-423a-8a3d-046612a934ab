import asyncio
import logging
import time
from datetime import datetime
from datetime import time as dt_time
from datetime import timedelta
from typing import Dict, Optional

import aiohttp
import pytz
from app.db.session import async_session
from app.schemas.live_price import LivePrice

# Logger
logger = logging.getLogger(__name__)

# Bangladesh timezone
BST = pytz.timezone('Asia/Dhaka')

# Market hours
MARKET_CHECK_TIME = dt_time(10, 15)
MARKET_CLOSE_TIME = dt_time(14, 30)
PRICE_UPDATE_INTERVAL = 5 * 60  # 5 minutes in seconds
CACHE_TIMEOUT = 15 * 60  # 15 minutes in seconds

# Cache for live prices
live_prices_cache: Dict[str, LivePrice] = {}
last_cache_access_time = time.time()

# Add circuit breaker for API failures
api_failure_count = 0
MAX_API_FAILURES = 3

async def check_market_status() -> bool:
    """
    Check the market status by calling the amarstock API with proper error handling
    """
    global api_failure_count
    
    url = "https://www.amarstock.com/Info/DSE"
    
    try:
        timeout = aiohttp.ClientTimeout(total=10)  # 10 second timeout
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    market_status = data.get('MarketStatus', 'Closed')
                    api_failure_count = 0  # Reset failure count on success
                    logger.info(f"Market status retrieved: {market_status}")
                    return market_status != 'Closed'
                else:
                    logger.warning(f"API returned status code: {response.status}")
                    api_failure_count += 1
                    
    except asyncio.TimeoutError:
        logger.error("Timeout while checking market status")
        api_failure_count += 1
    except Exception as e:
        logger.error(f"Error checking market status: {str(e)}")
        api_failure_count += 1
    
    # If we have too many failures, assume market is closed for safety
    if api_failure_count >= MAX_API_FAILURES:
        logger.error(f"Too many API failures ({api_failure_count}), assuming market is closed")
    
    return False

def is_not_friday(dt: datetime) -> bool:
    """
    Check if the given datetime is not Friday
    Returns False only for Friday, True for all other days
    """
    weekday = dt.weekday()  # Monday=0, Sunday=6
    return weekday != 4  # Friday is 4

def get_seconds_until_time(target_time: dt_time, from_datetime: Optional[datetime] = None) -> float:
    """
    Calculate seconds until a specific time, handling day transitions properly
    """
    if from_datetime is None:
        from_datetime = datetime.now(BST)
    
    target_datetime = datetime.combine(from_datetime.date(), target_time)
    target_datetime = BST.localize(target_datetime)
    
    # If target time has already passed today, schedule for tomorrow
    if target_datetime <= from_datetime:
        target_datetime += timedelta(days=1)
    
    return (target_datetime - from_datetime).total_seconds()

def should_update_prices() -> bool:
    """
    Check if we should update prices based on cache access time
    """
    current_time = time.time()
    cache_age = current_time - last_cache_access_time
    return cache_age < CACHE_TIMEOUT

async def run_market_session():
    """
    Handle a single market session (from market open check until close)
    """
    from app.api.endpoints.live_prices import update_live_prices_task
    
    logger.info("Starting market session monitoring")
    
    # Check if market is open
    is_market_open = await check_market_status()
    
    if not is_market_open:
        logger.info("Market is closed. No updates for today.")
        return
    
    logger.info("Market is open. Starting live price updates.")
    
    # Update prices until market closes
    session_start = datetime.now(BST)
    while True:
        current_time = datetime.now(BST)
        
        # Check if market should be closed
        if current_time.time() > MARKET_CLOSE_TIME:
            logger.info("Market session ended.")
            break
        
        # Check if we should update based on cache access
        if should_update_prices():
            try:
                async with async_session() as db:
                    await update_live_prices_task(db)
                logger.info("Live prices updated successfully.")
            except Exception as e:
                logger.error(f"Error updating live prices: {str(e)}")
        else:
            cache_age = time.time() - last_cache_access_time
            logger.info(f"Skipping price update - cache not accessed recently (age: {cache_age:.0f}s)")
        
        # Wait for next update
        await asyncio.sleep(PRICE_UPDATE_INTERVAL)
    
    logger.info("Market session completed.")

async def schedule_live_price_updates():
    """
    Main scheduler function that runs continuously
    """
    logger.info("Live price scheduler started")
    
    while True:
        try:
            now_bst = datetime.now(BST)
            
            # Skip if it's Friday
            if not is_not_friday(now_bst):
                logger.info("Skipping market operations on Friday")
                
                # Calculate seconds until next day (Saturday)
                tomorrow = now_bst + timedelta(days=1)
                seconds_until_tomorrow = get_seconds_until_time(MARKET_CHECK_TIME, tomorrow)
                
                logger.info(f"Next market check scheduled for: {tomorrow.strftime('%Y-%m-%d')} at {MARKET_CHECK_TIME}")
                await asyncio.sleep(seconds_until_tomorrow)
                continue
            
            # Wait until market check time if we're early
            if now_bst.time() < MARKET_CHECK_TIME:
                seconds_until_check = get_seconds_until_time(MARKET_CHECK_TIME, now_bst)
                logger.info(f"Waiting {seconds_until_check:.0f} seconds until market check time ({MARKET_CHECK_TIME})")
                await asyncio.sleep(seconds_until_check)
            
            # Run the market session
            await run_market_session()
            
            # Schedule next day's check
            tomorrow = now_bst + timedelta(days=1)
            seconds_until_next = get_seconds_until_time(MARKET_CHECK_TIME, tomorrow)
            logger.info(f"Next market check scheduled for: {tomorrow.strftime('%Y-%m-%d')} at {MARKET_CHECK_TIME}")
            await asyncio.sleep(seconds_until_next)
            
        except asyncio.CancelledError:
            logger.info("Live price scheduler shutting down gracefully")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in scheduler: {str(e)}")
            # Wait a bit before retrying to avoid tight error loops
            await asyncio.sleep(60)

def update_cache_access_time():
    """
    Update the timestamp of the last cache access
    """
    global last_cache_access_time
    last_cache_access_time = time.time()
    logger.debug(f"Cache access time updated: {datetime.fromtimestamp(last_cache_access_time).isoformat()}")

def add_active_user(user_id: str):
    """
    Record that a user is active (updates cache access time)
    """
    update_cache_access_time()
    logger.info(f"User {user_id} activity recorded")


def remove_active_user(user_id: str):
    """
    No-op function kept for backward compatibility
    """
    logger.info(f"User {user_id} activity ended")



