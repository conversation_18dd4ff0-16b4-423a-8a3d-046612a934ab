version: "3.8"

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    ports:
      - "3000:3000"
    depends_on:
      - backend
    environment:
      - NODE_ENV=production
      - AUTH_URL=http://localhost:3000
      - AUTH_SECRET=0788c201cb981bea46d2d971ba806627d1e9981b9094984c785795da7f21d492
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=0788c201cb981bea46d2d971ba806627d1e9981b9094984c785795da7f21d492
      - NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
    restart: unless-stopped
    # Add host.docker.internal to /etc/hosts for better connectivity
    extra_hosts:
      - "host.docker.internal:host-gateway"

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=root
      - POSTGRES_DB=dse_analyzen
      - SECRET_KEY=your-secret-key-for-jwt-encryption
    volumes:
      - ./backend:/app
    restart: unless-stopped

  postgres:
    image: postgres:17
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=root
      - POSTGRES_DB=dse_analyzen
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

volumes:
  postgres_data:
