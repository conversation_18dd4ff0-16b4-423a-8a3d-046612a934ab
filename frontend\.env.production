# Production environment variables
# Update these values for your production deployment

# NextAuth.js configuration
AUTH_URL=http://localhost:3000
AUTH_SECRET=0788c201cb981bea46d2d971ba806627d1e9981b9094984c785795da7f21d492

# Legacy NextAuth.js variables (for backward compatibility)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=0788c201cb981bea46d2d971ba806627d1e9981b9094984c785795da7f21d492

# API configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# Note: For actual production deployment, update:
# - AUTH_URL to your production domain (e.g., https://yourdomain.com)
# - NEXTAUTH_URL to your production domain (e.g., https://yourdomain.com)
# - NEXT_PUBLIC_API_URL to your production API URL
# - Generate a new AUTH_SECRET using: openssl rand -hex 32
