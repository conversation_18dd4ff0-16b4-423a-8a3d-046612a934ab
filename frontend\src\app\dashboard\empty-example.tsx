"use client";

import DashboardLayout from "@/components/dashboard-layout";
import PortfolioTable, {
  PortfolioHolding,
} from "@/components/portfolio/PortfolioTable";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";

export default function EmptyDashboardExample() {
  const { data: session } = useSession();
  const [userData, setUserData] = useState<any>(null);
  // Empty portfolio data to demonstrate the "No holdings yet" message
  const [portfolio, setPortfolio] = useState<PortfolioHolding[]>([]);

  useEffect(() => {
    if (session?.user?.accessToken) {
      fetchUserData();
    }
  }, [session]);

  const fetchUserData = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/me`,
        {
          headers: {
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setUserData(data);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  return (
    <DashboardLayout>
      <div>
        <h1 className="text-3xl font-bold mb-8">
          DSE Analyzen Dashboard (Empty Example)
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>User Profile</CardTitle>
              <CardDescription>Your account information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p>
                  <span className="font-semibold">Name:</span>{" "}
                  {session?.user?.name || "N/A"}
                </p>
                <p>
                  <span className="font-semibold">Email:</span>{" "}
                  {session?.user?.email}
                </p>
                <p>
                  <span className="font-semibold">Role:</span>{" "}
                  {session?.user?.isAdmin ? "Administrator" : "Regular User"}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and actions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button className="w-full">View Market Data</Button>
                <Button className="w-full" variant="outline">
                  Generate Reports
                </Button>
                {session?.user?.isAdmin && (
                  <Button className="w-full" variant="secondary">
                    Admin Settings
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Portfolio Section - Empty Example */}
        <PortfolioTable holdings={portfolio} />
      </div>
    </DashboardLayout>
  );
}
