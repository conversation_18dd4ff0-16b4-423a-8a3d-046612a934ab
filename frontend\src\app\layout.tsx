import { AuthProvider } from "@/components/auth-provider";
import { SuppressHydrationWarnings } from "@/utils/suppressHydrationWarnings";
import type { Metadata } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "DSE Analyzen",
  description: "Dhaka Stock Exchange Analysis Platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <SuppressHydrationWarnings>
          <AuthProvider>{children}</AuthProvider>
        </SuppressHydrationWarnings>
      </body>
    </html>
  );
}
