import type { User } from "next-auth";
import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

// Extend the User type to include our custom fields
export interface CustomUser extends User {
  id: string;
  email: string;
  name?: string;
  isAdmin: boolean;
  accessToken?: string;
}

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string;
      isAdmin: boolean;
      accessToken?: string;
    };
  }
}

declare module "@auth/core/jwt" {
  interface JWT {
    id: string;
    email: string;
    name?: string;
    isAdmin: boolean;
    accessToken?: string;
  }
}

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === "development",
  // Configure one or more authentication providers
  pages: {
    signIn: "/login",
    error: "/login", // Add error page
  },
  session: {
    strategy: "jwt", // Explicitly set the session strategy
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // Only update session once per day
  },
  // Configure callbacks
  callbacks: {
    redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
    async jwt({ token, user }) {
      // Initial sign in
      if (user) {
        // For credentials login
        return {
          ...token,
          id: user.id as string,
          email: user.email as string,
          name: user.name,
          isAdmin: (user as Record<string, any>).isAdmin as boolean,
          accessToken: (user as Record<string, any>).accessToken as string,
        };
      }

      return token;
    },
    async session({ session, token }) {
      // Return a properly typed Session object
      return {
        ...session,
        user: {
          id: token.id,
          email: token.email,
          name: token.name,
          isAdmin: token.isAdmin,
          accessToken: token.accessToken,
        },
      };
    },
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) return null;

        try {
          // Create form data for OAuth2 compatibility
          const formData = new URLSearchParams();
          formData.append("username", credentials.email as string); // OAuth2 expects 'username' field
          formData.append("password", credentials.password as string);

          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/x-www-form-urlencoded",
              },
              body: formData,
            }
          );

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.detail || "Invalid credentials");
          }

          return {
            id: data.id,
            email: data.email,
            name: data.full_name,
            isAdmin: data.is_admin,
            accessToken: data.access_token,
          };
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      },
    }),
  ],
});

