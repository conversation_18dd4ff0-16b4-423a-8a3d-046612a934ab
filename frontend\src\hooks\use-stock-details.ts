import { isMarketOpen } from "@/utils/utils";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useToast } from "./use-toast";

export interface StockDetails {
  trading_code: string;
  opening_price?: number;
  week_52_low?: number;
  week_52_high?: number;
  pe_ratio?: number;
  last_updated: string;
}

const DB_NAME = "StockDetailsDB";
const DB_VERSION = 1;
const STORE_NAME = "stockDetails";

class StockDetailsDB {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          const store = db.createObjectStore(STORE_NAME, {
            keyPath: "trading_code",
          });
          store.createIndex("last_updated", "last_updated", { unique: false });
        }
      };
    });
  }

  async getStockDetails(tradingCode: string): Promise<StockDetails | null> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readonly");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.get(tradingCode);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          const lastUpdated = new Date(result.last_updated);
          const now = new Date();
          const ageHours =
            (now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60);

          if (ageHours < 24) {
            resolve(result);
          } else {
            resolve(null);
          }
        } else {
          resolve(null);
        }
      };
    });
  }

  async setStockDetails(details: StockDetails): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readwrite");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.put(details);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async getAllStockDetails(): Promise<StockDetails[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readonly");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.getAll();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  async clearStaleData(): Promise<void> {
    if (!this.db) await this.init();
    const allDetails = await this.getAllStockDetails();
    const now = new Date();

    for (const details of allDetails) {
      const lastUpdated = new Date(details.last_updated);
      const ageHours =
        (now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60);

      if (ageHours >= 24) {
        await this.deleteStockDetails(details.trading_code);
      }
    }
  }

  async deleteStockDetails(tradingCode: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readwrite");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.delete(tradingCode);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }
}

const stockDetailsDB = new StockDetailsDB();

export function useStockDetails() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [stockDetails, setStockDetails] = useState<
    Record<string, StockDetails>
  >({});
  const [isLoading, setIsLoading] = useState(false);
  const lastFetchTimeRef = useRef<number>(0);

  useEffect(() => {
    stockDetailsDB.init().catch(console.error);
  }, []);

  const isScheduledUpdateTime = useCallback(async (): Promise<boolean> => {
    const now = new Date();
    const isOpen = await isMarketOpen();

    const hour = now.getHours();
    const minute = now.getMinutes();

    const isScheduledTime = hour === 10 && minute >= 30;

    return isOpen && isScheduledTime;
  }, []);

  const fetchStockDetailsFromAPI = useCallback(
    async (tradingCodes: string[]): Promise<Record<string, StockDetails>> => {
      if (!session?.user?.accessToken || !tradingCodes.length) return {};

      try {
        const response = await fetch(
          `${
            process.env.NEXT_PUBLIC_API_URL
          }/live-prices/stock-details?codes=${tradingCodes.join(",")}`,
          {
            headers: {
              Authorization: `Bearer ${session.user.accessToken}`,
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          return data.details || {};
        }
        return {};
      } catch (error) {
        console.error("Error fetching stock details:", error);
        return {};
      }
    },
    [session]
  );

  const fetchStockDetails = useCallback(
    async (tradingCodes: string[], forceRefresh = false) => {
      if (!tradingCodes.length) return;

      setIsLoading(true);
      const fetchedDetails: Record<string, StockDetails> = {};

      try {
        const shouldFetchFromApi = forceRefresh || isScheduledUpdateTime();

        if (shouldFetchFromApi) {
          const now = Date.now();
          if (now - lastFetchTimeRef.current < 3600000 && !forceRefresh) {
            setIsLoading(false);
            return;
          }
          lastFetchTimeRef.current = now;

          for (const code of tradingCodes) {
            const apiResult = await fetchStockDetailsFromAPI([code]);
            if (apiResult[code]) {
              fetchedDetails[code] = apiResult[code];
              await stockDetailsDB.setStockDetails(apiResult[code]);
            }
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
          await stockDetailsDB.clearStaleData();
        } else {
          for (const code of tradingCodes) {
            const cachedDetails = await stockDetailsDB.getStockDetails(code);
            if (cachedDetails) {
              fetchedDetails[code] = cachedDetails;
            }
          }
        }

        if (Object.keys(fetchedDetails).length > 0) {
          setStockDetails((prev) => ({ ...prev, ...fetchedDetails }));
        }
      } catch (error) {
        console.error("Error fetching stock details:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch stock details",
        });
      } finally {
        setIsLoading(false);
      }
    },
    [isScheduledUpdateTime, fetchStockDetailsFromAPI, toast]
  );

  const refreshStockDetails = useCallback(
    async (tradingCodes: string[]) => {
      if (!tradingCodes.length) return;
      await fetchStockDetails(tradingCodes, true);
      toast({
        title: "Refreshing Stock Details",
        description: "Stock details have been updated.",
      });
    },
    [fetchStockDetails, toast]
  );

  return {
    stockDetails,
    isLoading,
    fetchStockDetails,
    refreshStockDetails,
    isScheduledUpdateTime,
  };
}
