@echo off
echo Testing DSE Analyzen Production Build
echo =====================================

REM Check if we're in the right directory
if not exist "frontend\package.json" (
    echo Error: Please run this script from the project root directory
    exit /b 1
)

REM Navigate to frontend directory
cd frontend

echo 1. Building production version...
call npm run build

if %errorlevel% neq 0 (
    echo Error: Build failed
    exit /b 1
)

echo ✓ Build completed successfully
echo.

echo 2. Production build is ready!
echo.
echo To start the production server:
echo   cd frontend
echo   npm run start
echo.
echo Or use Docker:
echo   docker-compose -f docker-compose.prod.yml up -d
echo.
echo Production build test completed!
