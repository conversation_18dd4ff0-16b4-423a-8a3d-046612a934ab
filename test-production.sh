#!/bin/bash

# Test script for production build
echo "Testing DSE Analyzen Production Build"
echo "====================================="

# Check if we're in the right directory
if [ ! -f "frontend/package.json" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Navigate to frontend directory
cd frontend

echo "1. Building production version..."
npm run build

if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

echo "✓ Build completed successfully"

echo ""
echo "2. Starting production server on port 3001 (to avoid conflicts)..."
PORT=3001 npm run start &
SERVER_PID=$!

# Wait a moment for server to start
sleep 5

echo "3. Testing if server is responding..."
if curl -f http://localhost:3001 > /dev/null 2>&1; then
    echo "✓ Production server is running successfully on http://localhost:3001"
else
    echo "✗ Server is not responding"
fi

# Clean up
echo ""
echo "4. Stopping test server..."
kill $SERVER_PID 2>/dev/null

echo ""
echo "Production build test completed!"
echo "You can now run 'npm run start' in the frontend directory to start the production server."
